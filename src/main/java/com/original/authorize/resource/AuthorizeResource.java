package com.original.authorize.resource;

import com.original.authorize.domain.dto.LoginDto;
import com.original.authorize.domain.dto.RefreshTokenDto;
import com.original.authorize.domain.dto.RegisterDto;
import com.original.authorize.domain.vo.TokenVo;
import com.original.authorize.service.AuthorizeService;
import com.original.core.annotation.AccessLog;
import com.original.core.domain.vo.ResponseVo;
import com.original.core.enums.AccessType;
import com.original.system.user.domain.dto.ValidateUsernameUniqueDto;
import com.original.system.user.service.UserService;
import jakarta.annotation.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 授权资源控制器
 * 提供用户注册、登录等授权相关的REST API接口
 */
@CrossOrigin
@RestController
@RequestMapping("/v1/authorize")
public class AuthorizeResource {

    /**
     * 授权服务
     * 用于处理用户注册、登录等授权相关的业务逻辑
     */
    @Resource
    private AuthorizeService authorizeService;

    /**
     * 用户服务
     * 用于处理用户管理相关的业务逻辑
     */
    @Resource
    private UserService userService;

    /**
     * 校验用户名唯一性
     * 检查用户名是否已被使用
     *
     * @param validateUsernameUniqueDto 用户名校验数据传输对象
     * @return 校验结果响应实体，true表示可用，false表示已存在
     */
    @GetMapping("/username/unique")
    public ResponseEntity<ResponseVo<Boolean>> validateUsernameUnique(
            @Validated ValidateUsernameUniqueDto validateUsernameUniqueDto
    ) {
        return ResponseEntity.ok(userService.validateUsernameUnique(validateUsernameUniqueDto));
    }

    /**
     * 用户注册
     * 处理新用户注册请求
     *
     * @param registerDto 注册数据传输对象
     * @return 注册结果响应实体
     */
    @AccessLog(AccessType.REGISTER)
    @PostMapping("/register")
    public ResponseEntity<ResponseVo<Void>> register(@RequestBody @Validated RegisterDto registerDto) {
        return ResponseEntity.ok(authorizeService.register(registerDto));
    }

    /**
     * 用户登录
     * 处理用户登录请求，返回访问令牌和刷新令牌
     *
     * @param loginDto 登录数据传输对象
     * @return 登录结果响应实体，包含令牌信息
     */
    @AccessLog(AccessType.LOGIN)
    @PostMapping("/login")
    public ResponseEntity<ResponseVo<TokenVo>> login(@RequestBody @Validated LoginDto loginDto) {
        return ResponseEntity.ok(authorizeService.login(loginDto));
    }

    @GetMapping("/refresh-token")
    public ResponseEntity<ResponseVo<TokenVo>> refreshToken(@RequestBody @Validated RefreshTokenDto refreshTokenDto) {
        return ResponseEntity.ok(authorizeService.refreshToken(refreshTokenDto));
    }

}
