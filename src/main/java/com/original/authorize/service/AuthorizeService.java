package com.original.authorize.service;

import com.original.authorize.domain.dto.LoginDto;
import com.original.authorize.domain.dto.RefreshTokenDto;
import com.original.authorize.domain.dto.RegisterDto;
import com.original.authorize.domain.vo.TokenVo;
import com.original.core.domain.vo.ResponseVo;

/**
 * 授权服务接口
 * 提供用户注册、登录等授权相关的业务逻辑方法
 */
public interface AuthorizeService {

    /**
     * 用户注册
     * 处理新用户注册请求
     *
     * @param registerDto 注册数据传输对象
     * @return 注册结果响应对象
     */
    ResponseVo<Void> register(RegisterDto registerDto);

    /**
     * 用户登录
     * 处理用户登录请求，返回访问令牌和刷新令牌
     *
     * @param loginDto 登录数据传输对象
     * @return 登录结果响应对象，包含令牌信息
     */
    ResponseVo<TokenVo> login(LoginDto loginDto);

    ResponseVo<TokenVo> refreshToken(RefreshTokenDto refreshTokenDto);

}
