package com.original.authorize.service.impl;

import com.original.authorize.domain.dto.LoginDto;
import com.original.authorize.domain.dto.RefreshTokenDto;
import com.original.authorize.domain.dto.RegisterDto;
import com.original.authorize.domain.vo.TokenVo;
import com.original.authorize.service.AuthorizeService;
import com.original.core.config.properties.OriginalProperties;
import com.original.core.constant.SystemConstant;
import com.original.core.domain.bo.UserBo;
import com.original.core.domain.vo.ResponseVo;
import com.original.core.enums.ResponseMessage;
import com.original.core.exception.BusinessException;
import com.original.core.utils.JwtUtils;
import com.original.system.user.domain.dto.CreateUserDto;
import com.original.system.user.service.UserService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.MalformedClaimException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * 授权认证服务实现类
 * 负责处理用户授权认证相关的业务逻辑
 */
@Slf4j
@Service
public class AuthorizeServiceImpl implements AuthorizeService {

    /**
     * 用户服务
     * 用于处理用户相关的操作
     */
    @Resource
    private UserService userService;

    /**
     * Spring Security认证管理器
     * 用于处理用户认证
     */
    @Resource
    private AuthenticationManager authenticationManager;

    /**
     * Redis模板
     * 用于缓存用户登录信息
     */
    @Resource
    private RedisTemplate<String, UserBo> redisTemplate;

    /**
     * 系统配置属性
     */
    @Resource
    private OriginalProperties properties;

    /**
     * 用户注册实现
     * 将注册信息转换为用户创建DTO并调用用户服务创建用户
     *
     * @param registerDto 注册信息
     * @return 注册结果
     */
    @Override
    @Transactional
    public ResponseVo<Void> register(RegisterDto registerDto) {
        CreateUserDto createUserDto = CreateUserDto.of(registerDto);
        return userService.create(createUserDto);
    }

    /**
     * 用户登录实现
     * 1. 调用Spring Security进行认证
     * 2. 将用户信息存入Redis
     * 3. 生成访问令牌和刷新令牌
     *
     * @param loginDto 登录信息
     * @return 登录结果，包含令牌信息
     * @throws BusinessException 用户名或密码错误时抛出
     */
    @Override
    @Transactional(readOnly = true)
    public ResponseVo<TokenVo> login(LoginDto loginDto) {
        Authentication authentication;
        String username = loginDto.getUsername();
        String password = loginDto.getPassword();
        try {

            UsernamePasswordAuthenticationToken authenticationToken =
                    new UsernamePasswordAuthenticationToken(username, password);

            SecurityContextHolder.getContext().setAuthentication(authenticationToken);

            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (BadCredentialsException exception) {
            log.warn("Invalid login attempt for user [{}]", username, exception);
            throw new BusinessException(ResponseMessage.USERNAME_OR_PASSWORD_ERROR);
        } finally {

            SecurityContextHolder.clearContext();
        }

        UserBo loginUserBo = (UserBo) authentication.getPrincipal();

        redisTemplate.opsForValue().set(
                SystemConstant.REDIS_PREFIX_LOGIN + loginUserBo.getId(),

                loginUserBo,

                properties.getAuthorize().getRefreshTokenExpireTime(),

                TimeUnit.MINUTES
        );

        TokenVo tokenVo = TokenVo.of(
                JwtUtils.generateAccessJwt(String.valueOf(loginUserBo.getId())),
                JwtUtils.generateRefreshJwt(String.valueOf(loginUserBo.getId()))
        );

        log.info("User [{}] login successful", loginUserBo.getUsername());

        return ResponseVo.success(tokenVo);
    }

    /**
     * 刷新令牌实现
     * 1. 解析并验证访问令牌和刷新令牌
     * 2. 验证两个令牌的主体一致性
     * 3. 更新Redis中用户的过期时间
     * 4. 生成新的访问令牌和刷新令牌
     *
     * @param refreshTokenDto 刷新令牌请求DTO，包含访问令牌和刷新令牌
     * @return 刷新结果响应对象，包含新的令牌信息
     * @throws BusinessException 当令牌解析失败、验证失败或主体不一致时抛出
     */
    @Override
    @Transactional(readOnly = true)
    public ResponseVo<TokenVo> refreshToken(RefreshTokenDto refreshTokenDto) {
        // Parse and verify access token
        JwtClaims payloadAccessToken = JwtUtils
                .parseAccessJwt(refreshTokenDto.getAccessToken(), Boolean.FALSE)
                .orElseThrow(() -> new BusinessException(ResponseMessage.TOKEN_REFRESH_FAILED));

        // Parse and verify refresh token
        JwtClaims payloadRefreshToken = JwtUtils
                .parseRefreshJwt(refreshTokenDto.getRefreshToken(), Boolean.TRUE)
                .orElseThrow(() -> new BusinessException(ResponseMessage.TOKEN_REFRESH_FAILED));

        String accessTokenSubject;
        String refreshTokenSubject;
        try {
            // Extract subjects from both tokens
            accessTokenSubject = payloadAccessToken.getSubject();
            refreshTokenSubject = payloadRefreshToken.getSubject();

            // Verify that both tokens belong to the same user
            if (!accessTokenSubject.equals(refreshTokenSubject)) {
                log.warn("Token subject mismatch: access token subject [{}], refresh token subject [{}]",
                        accessTokenSubject, refreshTokenSubject);
                throw new BusinessException(ResponseMessage.TOKEN_REFRESH_FAILED);
            }

            // Update user expiration time in redis
            redisTemplate
                    .expire(
                            // key: user id
                            SystemConstant.REDIS_PREFIX_LOGIN + accessTokenSubject,
                            // expire time: refresh token expire time
                            properties.getAuthorize().getRefreshTokenExpireTime(),
                            // unit: minutes
                            TimeUnit.MINUTES
                    );
        } catch (MalformedClaimException exception) {
            log.error("Failed to extract subject from token: {}", exception.getMessage(), exception);
            throw new BusinessException(ResponseMessage.TOKEN_REFRESH_FAILED);
        }

        // Create new access token and refresh token
        TokenVo tokenVo = TokenVo.of(
                JwtUtils.generateAccessJwt(accessTokenSubject),
                JwtUtils.generateRefreshJwt(refreshTokenSubject)
        );

        log.info("Token refresh successful for user [{}]", accessTokenSubject);

        return ResponseVo.success(tokenVo);
    }

}
