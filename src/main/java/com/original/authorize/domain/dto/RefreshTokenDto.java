package com.original.authorize.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RefreshTokenDto implements Serializable {

    @Serial
    private static final long serialVersionUID = 4194914994300197380L;

    private String accessToken;

    private String refreshToken;

}
