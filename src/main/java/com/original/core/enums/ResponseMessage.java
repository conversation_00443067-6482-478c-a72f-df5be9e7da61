package com.original.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应消息枚举
 * 定义系统中使用的标准响应消息的国际化消息键
 */
@Getter
@AllArgsConstructor
public enum ResponseMessage {

    /**
     * 操作成功
     */
    SUCCESS("operation_successful"),

    /**
     * 未授权，需要登录
     */
    UNAUTHORIZED("unauthorized"),

    /**
     * 禁止访问，没有权限
     */
    FORBIDDEN("forbidden"),

    /**
     * 服务器内部错误
     */
    SERVER_INTERNAL_ERROR("server_internal_error"),

    /**
     * 请求方法不允许
     */
    METHOD_NOT_ALLOWED("method_not_allowed"),

    /**
     * 用户未找到
     */
    USER_NOT_FOUND("user_not_found"),

    /**
     * 旧密码错误
     */
    OLD_PASSWORD_ERROR("old_password_error"),

    /**
     * 用户名已被使用
     */
    USERNAME_ALREADY_USE("username_already_use"),

    /**
     * 多个用户名已被使用
     */
    USERNAMES_ALREADY_USE("usernames_already_use"),

    /**
     * 邮箱已存在
     */
    EMAILS_ALREADY_EXISTS("emails_already_exists"),

    /**
     * 手机号码已存在
     */
    PHONE_NUMBERS_ALREADY_EXISTS("phone_numbers_already_exists"),

    /**
     * 无效的角色集合
     */
    INVALID_ROLE_LIST("invalid_role_list"),

    /**
     * 角色未找到
     */
    ROLE_NOT_FOUND("role_not_found"),

    /**
     * 角色名称已存在
     */
    ROLE_NAME_ALREADY_EXISTS("role_name_already_exists"),

    /**
     * 无法删除角色
     */
    UNABLE_DELETE_ROLES("unable_delete_roles"),

    /**
     * 权限未找到
     */
    PERMISSION_NOT_FOUND("permission_not_found"),

    /**
     * 权限名称已存在
     */
    PERMISSION_NAME_ALREADY_EXISTS("permission_name_already_exists"),

    /**
     * 权限标识已存在
     */
    AUTHORITY_ALREADY_EXISTS("authority_already_exists"),

    /**
     * 无效的父级ID
     */
    INVALID_PARENT_ID("invalid_parent_id"),

    /**
     * 无法删除权限
     */
    UNABLE_DELETE_PERMISSION("unable_delete_permission"),

    /**
     * 无效的权限集合
     */
    INVALID_PERMISSION_LIST("invalid_permission_list"),

    /**
     * 用户名或密码错误
     */
    USERNAME_OR_PASSWORD_ERROR("username_or_password_error"),

    /**
     * 导入失败
     */
    IMPORT_FAILED("import_failed"),

    /**
     * 参数是必需的
     */
    PARAMETER_IS_REQUIRED("parameter_is_required"),

    /**
     * 无效的Excel文件
     */
    INVALID_EXCEL_FILE("invalid_excel_file"),

    /**
     * 用户信息是必需的
     */
    DATA_INFORMATION_IS_REQUIRED("data_information_is_required"),

    /**
     * 不支持的文件类型
     */
    UNSUPPORTED_FILE_TYPE("unsupported_file_type"),

    /**
     * 无效的日期格式
     */
    INVALID_DATE_FORMAT("invalid_date_format"),

    /**
     * 无效的数字格式
     */
    INVALID_NUMBER_FORMAT("invalid_number_format"),

    /**
     * 不支持的图片类型
     */
    UNSUPPORTED_IMAGE_TYPE("unsupported_image_type"),

    /**
     * 文件类型未找到
     */
    FILE_TYPE_NOT_FOUND("file_type_not_found"),

    /**
     * 文件类型名称已存在
     */
    FILE_TYPE_NAME_ALREADY_EXISTS("file_type_name_already_exists"),

    /**
     * 无法删除文件类型
     */
    UNABLE_DELETE_FILE_TYPES("unable_delete_file_types"),

    /**
     * 文件扩展名已存在
     */
    EXTENSION_ALREADY_EXISTS("extension_already_exists"),

    /**
     * MIME类型已存在
     */
    MIME_TYPE_ALREADY_EXISTS("mime_type_already_exists"),

    /**
     * 文件元数据不存在
     */
    FILE_METADATA_NOT_FOUND("file_metadata_not_found"),

    /**
     * 无效的文件类型ID
     */
    INVALID_FILE_TYPE_ID("invalid_file_type_id"),

    /**
     * 文件大小超出限制
     */
    FILE_SIZE_EXCEEDS_LIMIT("file_size_exceeds_limit"),

    /**
     * 任务名称已存在
     */
    TASK_NAME_ALREADY_EXISTS("task_name_already_exists"),

    /**
     * 任务未找到
     */
    TASK_NOT_FOUND("task_not_found"),

    /**
     * 无效的调用目标，不再白名单内
     */
    INVALID_INVOKE_TARGET_WHITE_LIST("invalid_invoke_target_white_list"),

    /**
     * 无效的调用目标，不允许HTTP(S)调用
     */
    INVOKE_TARGET_NOT_ALLOWED_HTTP("invoke_target_not_allowed_http"),

    /**
     * 无效的调用目标，不允许LDAP(S)调用
     */
    INVOKE_TARGET_NOT_ALLOWED_LDAP("invoke_target_not_allowed_ldap"),

    /**
     * 无效的调用目标，不允许RMI调用
     */
    INVOKE_TARGET_NOT_ALLOWED_RMI("invoke_target_not_allowed_rmi"),

    /**
     * 无效的Cron表达式
     */
    INVALID_CRON_EXPRESSION("invalid_cron_expression"),

    /**
     * 任务不处于正常状态，无法执行
     */
    TASK_NOT_IN_NORMAL_STATUS("task_not_in_normal_status"),

    /**
     * 刷新令牌失败
     */
    TOKEN_REFRESH_FAILED("token_refresh_failed")
    ;

    /**
     * 消息键值
     * 用于从国际化资源文件中获取对应的消息文本
     */
    private final String value;

}
