operation_successful=Operation Successful
unauthorized=Unauthorized. Please log in first.
forbidden=Access to this resource is forbidden.
method_not_allowed=Method not allowed: \"{0}\". Please use a valid method.
server_internal_error=Server internal error. Please try again later.
old_password_error=Old password is incorrect.
user_not_found=User not found.
username_is_required=Username is required.
invalid_username_length=Username must be between {min} and {max} characters long.
password_is_required=Password is required.
invalid_password_length=Password must be between {min} and {max} characters long.
invalid_password_format=Password must contain at least number, uppercase letter, and lowercase letter.
old_password_is_required=Old password is required.
new_password_is_required=New password is required.
confirm_password_is_required=Confirm password is required.
entered_passwords_differ=Confirm password and password do not match.
username_already_use=Username is already in use.
usernames_already_use=Data import failed: usernames has already been used: \"{0}\".
emails_already_exists=Data import failed: emails has already been used: \"{0}\".
phone_numbers_already_exists=Data import failed: phone numbers already exists has already been used: \"{0}\".
username_or_password_error=Username or password error.
invalid_gender=Invalid gender. Allowed values: 1(male), 1(female).
invalid_birthday=Invalid birthday.
role_list_is_required=Role list is required.
invalid_role_list=Invalid role list.
id_is_required=ID is required.
role_name_is_required=Role name is required.
invalid_role_name_length=Role name cannot exceed {max} characters.
role_not_found=Role not found.
role_name_already_exists=Role name already exists.
unable_delete_roles=Deletion failed: roles has already been use: \"{0}\".
parent_node_is_required=Parent node is required.
permission_name_is_required=Permission name is required.
invalid_permission_name_length=Permission name cannot exceed {max} characters.
authority_is_required=Authority is required.
invalid_authority_length=Authority cannot exceed {max} characters.
permission_not_found=Permission not found.
permission_name_already_exists=Permission name already exists.
authority_already_exists=Authority already exists.
invalid_parent_id=Invalid parent node.
unable_delete_permission=Delete failed. Permission or its child node is already in use: \"{0}\".
permission_list_is_required=Permission list is required.
invalid_permission_list=Invalid permission list.
sort_is_required=Sort is required.
invalid_sort_size=Invalid sort size. Range: {min}-{max}.
invalid_permission_type=Invalid permission type. Allowed values: 1(group), 2(template), 3(link), 4(button).
link_target_is_required=Link target is required.
invalid_link_target=Invalid link target. Allowed values: 1(_self), 2(_blank).
router_path_is_required=Router path is required.
invalid_router_path_format=Invalid router path format (must start with \"/\").
component_path_is_required=Component path is required.
invalid_component_path_format=Invalid component path format.
keep_alive_is_required=Component cache status is required.
import_failed=Data import failed: Error in row {0}, column {1}: {1}. Please check and re-upload.
invalid_email_format=Invalid email format.
invalid_phone_number_format=Invalid phone number format.
permission_type_is_required=Permission type is required.
invalid_email_length=Email cannot exceed {max} characters.
invalid_icon_length=Icon cannot exceed {max} characters.
invalid_router_path_length=Router path cannot exceed {max} characters.
invalid_component_path_length=Component path cannot exceed {max} characters.
invalid_permission_status=Invalid permission status. Allowed values: 0(disabled), 1(enabled).
link_address_is_required=Link address is required
invalid_link_address_length=Link address cannot exceed {max} characters.
invalid_link_address_format=invalid link address.
user_id_list_is_required=User ID list is required.
parameter_is_required=Parameter \"{0}\" is required.
file_is_required=File is required.
invalid_excel_file=Invalid Excel file. Please upload a file in .xls or .xlsx format.
data_information_is_required=Data information is required. Please check the data and re-upload.
role_id_list_is_required=Role ID list is required.
file_list_is_required=File list is required.
unsupported_file_type=Unsupported file type: \"{0}\".
invalid_date_format=Invalid date format.
invalid_number_format=invalid number format.
unsupported_image_type=Unsupported image type. Expected formats: PNG, JPG.
file_type_name_is_required=File type name is required.
invalid_file_type_name_length=File type name cannot exceed {max} characters.
upload_size_unit_is_required=Upload size unit is required.
invalid_upload_size_unit=invalid upload size unit. Allowed values: 1(Bytes), 2(KB), 3(MB), 4(GB).
file_type_not_found=File type not found.
file_type_name_already_exists=File type name already exists.
max_upload_limit_size_is_required=Max upload limit size is required.
invalid_file_size_limit=Invalid file size limit. Max: 5GB.
unable_delete_file_types=Deletion failed: file types are already in use: "{0}".
file_type_id_list_is_required=File type ID list is required.
file_metadata_id_list_is_required=File metadata ID list is required.
file_extension_is_required=File extension is required.
invalid_file_extension_length=File extension cannot exceed {max} characters.
mime_type_is_required=MIME type is required.
invalid_mime_type_length=MIME type cannot exceed {max} characters.
extension_already_exists=File extension already exists.
mime_type_already_exists=MIME type already exists.
file_metadata_not_found=File metadata not found.
file_type_id_is_required=File type ID is required.
invalid_file_type_id=Invalid file type.
file_size_exceeds_limit=File size exceeds limit. File size: {0} {2}, Max allowed size: {1} {2}.
task_name_is_required=Task name is required.
task_group_is_required=Task group is required.
invoke_target_is_required=Invoke target is required.
cron_expression_is_required=Cron expression is required.
misfire_policy_is_required=Misfire policy is required.
concurrent_is_required=Concurrent execution flag is required.
task_status_is_required=Task status is required.
task_not_found=Task not found.
task_name_already_exists=Task name already exists.
invalid_task_status=Invalid task status. Allowed values: 0(pause), 1(normal).
invalid_misfire_policy=Invalid misfire policy. Allowed values: 1(default), 2(ignore misfires), 3(fire and proceed), 4(do nothing).
invalid_task_group=Invalid task group. Allowed values: 1(default), 2(system).
invalid_task_name_length=Task name cannot exceed {max} characters.
invalid_invoke_target_length=Invoke target cannot exceed {max} characters.
invalid_invoke_target_white_list=Invoke target not in white list.
invoke_target_not_allowed_http=Invoke target does not allow \"http (s)\" call.
invoke_target_not_allowed_ldap=Invoke target does not allow \"ldap (s)\" call.
invoke_target_not_allowed_rmi=Invoke target does not allow \"rmi\" call.
invalid_cron_expression=Invalid CRON expression.
task_id_list_is_required=Task ID list is required.
task_not_in_normal_status=Task is not in a NORMAL state and cannot be executed.
token_refresh_failed=Token refresh failed.