operation_successful=操作成功
unauthorized=未授权，请先登录
forbidden=访问此资源被禁止
method_not_allowed=不允许的请求方式：“{0}”，请使用有效的请求方式
server_internal_error=服务器内部错误，请稍后再试
old_password_error=旧密码不正确
user_not_found=用户未找到
username_is_required=用户名不能为空
invalid_username_length=用户名长度必需在 {min}-{max} 个字符之间
password_is_required=密码不能为空
invalid_password_length=密码长度必需在 {min}-{max} 个字符之间
invalid_password_format=密码必需包含数字、大写字母和小写字母
old_password_is_required=旧密码不能为空
new_password_is_required=新密码不能为空
confirm_password_is_required=确认密码不能为空
entered_passwords_differ=确认密码和密码不匹配
username_already_use=用户名已被使用
usernames_already_use=导入数据失败：以下用户名已被使用：“{0}”
emails_already_exists=导入数据失败：以下电子邮箱已被使用：“{0}”
phone_numbers_already_exists=导入数据失败：以下手机号码已被使用：“{0}”
username_or_password_error=用户名或密码错误
invalid_gender=无效的性别，允许的值：1（男）、1（女）
invalid_birthday=无效的生日
role_list_is_required=角色列表不能为空
invalid_role_list=无效的角色列表
id_is_required=ID不能为空
role_name_is_required=角色名称不能为空
invalid_role_name_length=角色名称不能超过 {max} 个字符
role_not_found=角色未找到
role_name_already_exists=角色名称已存在
unable_delete_roles=删除失败，以下角色已被使用：“{0}”
parent_node_is_required=父节点不能为空
permission_name_is_required=权限名称不能为空
invalid_permission_name_length=权限名称不能超过 {max} 个字符
authority_is_required=权限标识不能为空
invalid_authority_length=权限标识不能超过 {max} 个字符
permission_not_found=权限未找到
permission_name_already_exists=权限名称已存在
authority_already_exists=权限标识已存在
invalid_parent_id=无效的父节点
unable_delete_permission=删除失败，该权限或其子节点已被使用：“{0}”
permission_list_is_required=权限列表不能为空
invalid_permission_list=无效的权限列表
sort_is_required=排序不能为空
invalid_sort_size=排序值无效，有效范围：{min}-{max}
invalid_permission_type=无效的权限类型，允许的值：1（分组）、2（模板）、3（链接）、4（按钮）
link_target_is_required=链接目标不能为空
invalid_link_target=无效的链接目标，允许的值：1（_self）、2（_blank）
router_path_is_required=路由路径不能为空
invalid_router_path_format=路由路径格式无效（必须以“/”开头）
component_path_is_required=组件路径不能为空
invalid_component_path_format=组件路径格式无效
keep_alive_is_required=组件缓存状态不能为空
import_failed=导入数据失败：第{0}行第{1}列存在错误：{2}，请检查后重新上传
invalid_email_format=无效的电子邮箱格式
invalid_phone_number_format=无效的手机号码格式
permission_type_is_required=权限类型不能为空
invalid_email_length=电子邮箱不能超过 {max} 个字符
invalid_icon_length=图标不能超过 {max} 个字符
invalid_router_path_length=路由路径不能超过 {max} 个字符
invalid_component_path_length=组件路径不能超过 {max} 个字符
invalid_permission_status=无效的权限状态，允许的值：0（禁用）、1（启用）
link_address_is_required=链接地址不能为空
invalid_link_address_length=链接地址不能超过 {max} 个字符
invalid_link_address_format=无效的链接地址
user_id_list_is_required=用户ID列表不能为空
parameter_is_required=参数 “{0}” 是必须的
file_is_required=文件不能为空
invalid_excel_file=无效的Excel文件，请上传.xls或.xlsx格式的文件
data_information_is_required=数据信息不能为空，请检查数据后重新上传
role_id_list_is_required=角色ID列表不能为空
file_list_is_required=文件列表不能为空
unsupported_file_type=不支持的文件类型：“{0}”
invalid_date_format=无效的日期格式
invalid_number_format=无效的数字格式
unsupported_image_type=不支持的图片类型，预期格式：PNG、JPG
file_type_name_is_required=文件类型名称不能为空
invalid_file_type_name_length=文件类型名称不能超过 {max} 个字符
upload_size_unit_is_required=上传大小单位不能为空
invalid_upload_size_unit=无效的上传大小单位，允许的值：1（Bytes）、2（KB）、3（MB）、4（GB）
file_type_not_found=文件类型未找到
file_type_name_already_exists=文件类型名称已存在
max_upload_limit_size_is_required=最大的上传限制大小不能为空
invalid_file_size_limit=无效的文件大小限制，最大：5GB
unable_delete_file_types=删除失败，以下文件类型已被使用：“{0}”
file_type_id_list_is_required=文件类型ID列表不能为空
file_metadata_id_list_is_required=文件元数据ID列表不能为空
file_extension_is_required=文件扩展名不能为空
invalid_file_extension_length=文件扩展名不能超过 {max} 个字符
mime_type_is_required=MIME类型不能为空
invalid_mime_type_length=MIME类型不能超过 {max} 个字符
extension_already_exists=文件扩展名已存在
mime_type_already_exists=MIME类型已存在
file_metadata_not_found=文件元数据不存在
file_type_id_is_required=文件类型ID不能为空
invalid_file_type_id=无效的文件类型
file_size_exceeds_limit=文件大小超出限制，文件大小：{0} {2}，最大允许的大小：{1} {2}
task_name_is_required=任务名称不能为空
task_group_is_required=任务组不能为空
invoke_target_is_required=调用目标不能为空
cron_expression_is_required=Cron表达式不能为空
misfire_policy_is_required=任务失败策略不能为空
concurrent_is_required=并发执行标志不能为空
task_status_is_required=任务状态不能为空
task_not_found=任务未找到
task_name_already_exists=任务名称已存在
invalid_task_status=无效的任务状态，允许的值：0（暂停）、1（正常）
invalid_misfire_policy=无效的任务失败策略，允许的值：1（默认）、2（忽略失败）、3（触发并继续）、4（不做任何处理）
invalid_task_group=无效的任务组，允许的值：1（默认）、2（系统）
invalid_task_name_length=任务名称不能超过 {max} 个字符
invalid_invoke_target_length=调用目标不能超过 {max} 个字符
invalid_invoke_target_white_list=调用目标不在白名单内
invoke_target_not_allowed_http=调用目标不允许“http(s)”调用
invoke_target_not_allowed_ldap=调用目标不允许“ldap(s)”调用
invoke_target_not_allowed_rmi=调用目标不允许“rmi”调用
invalid_cron_expression=无效的Cron表达式
task_id_list_is_required=任务ID列表不能为空
task_not_in_normal_status=任务不处于正常状态，无法执行
token_refresh_failed=刷新令牌失败